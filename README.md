# Valdi

A powerful cross-platform UI framework that brings native performance to mobile development.

## Quick Links

- [Getting Started Guide](./docs/INSTALL.md)
- [Documentation](./docs/README.md)
- [Codelabs](./docs/docs/start-code-lab.md)
- [API Reference](./docs/generated-docs/valdi-generated.md)
- [Component Library](https://github.com/Snapchat/Valdi_Widgets)

## Why Choose <PERSON>di?

For the past 8 years, <PERSON><PERSON> has been the secret weapon behind Snapchat's rapid product development. Now open-sourced, we're excited to share this powerful framework with developers worldwide, helping teams of all sizes build better mobile applications.

### Key Benefits

🚀 **Native Performance**
- Leverages native platform components for optimal performance
- Seamless integration with existing native code

🛠 **Flexible Integration**
- Use it for individual components or entire applications
- Share code between Android and iOS effortlessly
- Mix and match with existing native codebases

💪 **Battle-Tested at Scale**
- Powers one of the world's largest mobile applications
- Manages 2M+ lines of cross-platform TypeScript code
- Proven in production with millions of daily users

⚡️ **Developer Experience**
- Instant hot reloading for rapid development
- Out-of-the-box debugging and profiling tools with VSCode
- Seamless Bazel build system integration

### Core Features

**Native Integration**
- Type-safe code generation for Kotlin and Objective-C
- Native component embedding support
- Direct access to C++, Kotlin, Objective-C, and Swift

**Performance Optimizations**
- Fast incremental updates
- Lazy module loading
- Automatic view recycling
- Optimized JavaScript FFI

**Rich Feature Set**
- Flexbox layout system
- Multi-threading with worker threads
- Built-in RTL support
- Offscreen rendering capabilities
- Comprehensive accessibility features
- Advanced gesture system
- Native animations
- Main thread synchronization
- Unit testing framework
- Native protobuf support

## Need Help?

Join our [Discord community](https://discord.gg/sqMERrCVYF) for support and discussions.

## Contributing

Please follow the [contributing](./CONTRIBUTING.md) guidelines.

## License

Valdi is made available under the MIT [License.](./LICENSE.md) file.
