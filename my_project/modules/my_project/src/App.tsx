import { StatefulComponent } from 'valdi_core/src/Component';
import { ImageView, Label, View } from 'valdi_tsx/src/NativeTemplateElements';
import { Style } from 'valdi_core/src/Style';
import { systemBoldFont, systemFont } from 'valdi_core/src/SystemFont';
import { AnimationCurve } from 'valdi_core/src/AnimationOptions';
import { DragEvent, PinchEvent } from 'valdi_tsx/src/GestureEvents';
import res from '../res';

/**
 * @ViewModel
 * @ExportModel
 */
export interface AppViewModel {}

/**
 * @Context
 * @ExportModel
 */
export interface AppComponentContext {}

interface State {
  // Animation states for different interactive elements
  logoScale: number;
  logoRotation: number;
  logoOpacity: number;

  // Color animation states
  backgroundHue: number;

  // Transform states for draggable element
  dragX: number;
  dragY: number;

  // Pinch/zoom states
  pinchScale: number;

  // Floating elements animation
  floatingOffset: number;
}

/**
 * @Component
 * @ExportModel
 */
export class App extends StatefulComponent<AppViewModel, AppComponentContext> {
  state: State = {
    logoScale: 1,
    logoRotation: 0,
    logoOpacity: 1,
    backgroundHue: 200,
    dragX: 0,
    dragY: 0,
    pinchScale: 1,
    floatingOffset: 0,
  };

  private floatingAnimationInterval?: number;

  onCreate(): void {
    console.log('Valdi Animation Showcase - App created!');
    // Start continuous floating animation for background elements
    this.startFloatingAnimation();
  }

  onDestroy(): void {
    console.log('Valdi Animation Showcase - App destroyed!');
    if (this.floatingAnimationInterval) {
      clearInterval(this.floatingAnimationInterval);
    }
  }

  // Continuous floating animation for background elements
  private startFloatingAnimation = (): void => {
    this.floatingAnimationInterval = setInterval(() => {
      // Use Valdi's spring animation for smooth floating effect
      this.animate(
        {
          stiffness: 100,
          damping: 15,
          duration: 2,
        },
        () => {
          this.setState({
            floatingOffset: this.state.floatingOffset === 0 ? 20 : 0,
          });
        }
      );
    }, 3000);
  };

  // Logo tap animation - combines scale, rotation, and opacity
  private onLogoTap = (): void => {
    // Valdi spring animation for bouncy logo interaction
    this.animate(
      {
        stiffness: 300,
        damping: 20,
      },
      () => {
        this.setState({
          logoScale: 1.3,
          logoRotation: this.state.logoRotation + Math.PI / 4, // 45 degrees
          logoOpacity: 0.8,
        });
      }
    );

    // Return to original state after animation
    setTimeout(() => {
      this.animate(
        {
          duration: 0.5,
          curve: AnimationCurve.EaseOut,
        },
        () => {
          this.setState({
            logoScale: 1,
            logoOpacity: 1,
          });
        }
      );
    }, 300);
  };

  // Color button animation - cycles through background colors
  private onColorButtonTap = (): void => {
    this.animate(
      {
        duration: 0.8,
        curve: AnimationCurve.EaseInOut,
      },
      () => {
        this.setState({
          backgroundHue: (this.state.backgroundHue + 60) % 360,
        });
      }
    );
  };



  // Drag gesture handler for draggable element
  private onDragMove = (event: DragEvent): void => {
    // Real-time drag animation without explicit animation call for smooth tracking
    this.setState({
      dragX: event.x - 50, // Offset for center positioning
      dragY: event.y - 50,
    });
  };

  // Pinch gesture handler for scalable element
  private onPinchGesture = (event: PinchEvent): void => {
    // Real-time pinch scaling
    this.setState({
      pinchScale: Math.max(0.5, Math.min(3, event.scale)),
    });
  };

  onRender(): void {
    const backgroundColor = `hsl(${this.state.backgroundHue}, 70%, 95%)`;

    <view style={styles.main.extend({ backgroundColor })}>
      {/* Header */}
      <view style={styles.header}>
        <label style={styles.title} value="🎨 Valdi Animation Showcase" />
        <label style={styles.subtitle} value="Tap, drag, and interact with elements below!" />
      </view>

      {/* Interactive Logo - Tap for scale/rotation animation */}
      <view style={styles.logoContainer}>
        <image
          style={styles.logo.extend({
            scaleX: this.state.logoScale,
            scaleY: this.state.logoScale,
            rotation: this.state.logoRotation,
            opacity: this.state.logoOpacity,
          })}
          src={res.valdi}
          onTap={this.onLogoTap}
        />
        <label style={styles.logoLabel} value="Tap the logo!" />
      </view>

      {/* Color Change Button */}
      <view style={styles.buttonContainer}>
        <view
          style={styles.colorButton}
          onTap={this.onColorButtonTap}
        >
          <label style={styles.buttonText} value="🌈 Change Background Color" />
        </view>
      </view>

      {/* Draggable Element */}
      <view style={styles.dragContainer}>
        <label style={styles.instructionText} value="Drag the circle below:" />
        <view
          style={styles.draggableElement.extend({
            translationX: this.state.dragX,
            translationY: this.state.dragY,
          })}
          onDrag={this.onDragMove}
        >
          <label style={styles.dragText} value="🔵" />
        </view>
      </view>

      {/* Pinch/Scale Element */}
      <view style={styles.pinchContainer}>
        <label style={styles.instructionText} value="Pinch to scale:" />
        <view
          style={styles.pinchableElement.extend({
            scaleX: this.state.pinchScale,
            scaleY: this.state.pinchScale,
          })}
          onPinch={this.onPinchGesture}
        >
          <label style={styles.pinchText} value="🟡" />
        </view>
      </view>

      {/* Floating Background Elements */}
      <view style={styles.floatingContainer}>
        <view
          style={styles.floatingElement.extend({
            translationY: this.state.floatingOffset,
          })}
        >
          <label style={styles.floatingText} value="✨" />
        </view>
        <view
          style={styles.floatingElement2.extend({
            translationY: -this.state.floatingOffset,
          })}
        >
          <label style={styles.floatingText} value="⭐" />
        </view>
      </view>
    </view>;
  }
}

const styles = {
  // Main container with flexible layout
  main: new Style<View>({
    backgroundColor: 'white',
    padding: 20,
    height: '100%',
  }),

  // Header section
  header: new Style<View>({
    alignItems: 'center',
    marginBottom: 30,
  }),

  title: new Style<Label>({
    color: '#2c3e50',
    font: systemBoldFont(28),
    textAlign: 'center',
    marginBottom: 10,
  }),

  subtitle: new Style<Label>({
    color: '#7f8c8d',
    font: systemFont(16),
    textAlign: 'center',
    numberOfLines: 0,
  }),

  // Logo section with interactive animations
  logoContainer: new Style<View>({
    alignItems: 'center',
    marginBottom: 30,
  }),

  logo: new Style<ImageView>({
    width: 100,
    height: 100,
    borderRadius: 20,
    boxShadow: '0 4 8 rgba(0, 0, 0, 0.2)',
  }),

  logoLabel: new Style<Label>({
    color: '#34495e',
    font: systemFont(14),
    marginTop: 10,
    textAlign: 'center',
  }),

  // Button section
  buttonContainer: new Style<View>({
    alignItems: 'center',
    marginBottom: 30,
  }),

  colorButton: new Style<View>({
    backgroundColor: '#3498db',
    borderRadius: 25,
    padding: 15,
    boxShadow: '0 2 4 rgba(0, 0, 0, 0.1)',
  }),

  buttonText: new Style<Label>({
    color: 'white',
    font: systemBoldFont(16),
    textAlign: 'center',
  }),

  // Drag section
  dragContainer: new Style<View>({
    alignItems: 'center',
    marginBottom: 30,
    height: 120,
  }),

  instructionText: new Style<Label>({
    color: '#2c3e50',
    font: systemFont(14),
    marginBottom: 15,
    textAlign: 'center',
  }),

  draggableElement: new Style<View>({
    width: 60,
    height: 60,
    backgroundColor: '#e74c3c',
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 2 4 rgba(0, 0, 0, 0.15)',
  }),

  dragText: new Style<Label>({
    font: systemFont(24),
  }),

  // Pinch section
  pinchContainer: new Style<View>({
    alignItems: 'center',
    marginBottom: 30,
  }),

  pinchableElement: new Style<View>({
    width: 80,
    height: 80,
    backgroundColor: '#f39c12',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 2 4 rgba(0, 0, 0, 0.15)',
  }),

  pinchText: new Style<Label>({
    font: systemFont(32),
  }),

  // Floating elements
  floatingContainer: new Style<View>({
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  }),

  floatingElement: new Style<View>({
    position: 'absolute',
    top: 100,
    left: 50,
  }),

  floatingElement2: new Style<View>({
    position: 'absolute',
    top: 200,
    right: 50,
  }),

  floatingText: new Style<Label>({
    font: systemFont(24),
    opacity: 0.6,
  }),
};
